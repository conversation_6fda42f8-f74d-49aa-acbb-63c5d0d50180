<?php
// 处理 AJAX 请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    if ($_POST['action'] === 'send_mail') {
        // 这里应该包含原有的 send_mail 逻辑
        // 暂时返回成功响应
        echo json_encode(['success' => true, 'message' => '邮件发送成功！']);
        exit;
    }
}

include 'head.php';
include_once './user/config.php';
$t = time();

// 读取物品数据
$items = [];
if (file_exists("user/player.txt")) {
    $file = fopen("user/player.txt", "r");
    while(!feof($file)) {
        $line = fgets($file);
        if (trim($line)) {
            $txts = explode(';', $line);
            if (count($txts) >= 3) {
                $items[] = [
                    'value' => trim($txts[0]),
                    'label' => trim($txts[1]),
                    'desc' => trim($txts[2])
                ];
            }
        }
    }
    fclose($file);
}

// 读取服务器数据
$servers = [];
if (isset($quarr)) {
    foreach($quarr as $key => $value) {
        if($value['hidde'] != true) {
            $servers[] = [
                'value' => $key,
                'label' => $value['name']
            ];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"/>
    <title>魔道修仙-物品后台</title>
    <meta name="applicable-device" content="pc,mobile" />
    <!-- Element UI Plus CSS -->
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/element-plus/dist/index.css" />
    <!-- Vue 3 -->
    <script src="//cdn.jsdelivr.net/npm/vue@3"></script>
    <!-- Element UI Plus JS -->
    <script src="//cdn.jsdelivr.net/npm/element-plus"></script>
    <!-- Axios for HTTP requests -->
    <script src="//cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
    :root {
        --primary-color: #ff7eb9;
        --secondary-color: #ff65a3;
        --accent-color: #7afcff;
        --text-color: #5a3d5c;
        --bg-color: #fff0f5;
        --card-bg: rgba(255, 255, 255, 0.9);
        --shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
    }

    body {
        font-family: 'Noto Sans SC', sans-serif;
        background: var(--bg-color) url('<?=isset($conf['gro']) ? $conf['gro'] : ''?>') no-repeat center center fixed;
        background-size: cover;
        color: var(--text-color);
        min-height: 100vh;
        margin: 0;
        padding: 20px 0;
        position: relative;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 240, 245, 0.7);
        z-index: -1;
    }

    .app-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        position: relative;
    }

    .main-card {
        background: var(--card-bg);
        border-radius: 15px;
        box-shadow: var(--shadow);
        border: 1px solid rgba(255, 126, 185, 0.2);
        backdrop-filter: blur(5px);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .main-card:hover {
        box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
    }

    .card-header {
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 20px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
    }

    .card-body {
        padding: 30px;
    }

    /* Element UI Plus 自定义样式 */
    .el-form-item {
        margin-bottom: 25px;
    }

    .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 0 0 1px #ffb6c1 inset;
        transition: all 0.3s;
    }

    .el-input__wrapper:hover {
        box-shadow: 0 0 0 1px var(--primary-color) inset;
    }

    .el-input__wrapper.is-focus {
        box-shadow: 0 0 0 1px var(--primary-color) inset, 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    .el-select .el-input__wrapper {
        border-radius: 8px;
    }

    .el-button--primary {
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        border: none;
        border-radius: 8px;
        font-weight: bold;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(255, 105, 180, 0.3);
    }

    .el-button--primary:hover {
        background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 105, 180, 0.4);
    }

    .el-button--primary:active {
        transform: translateY(0);
    }

    /* 装饰元素 */
    .decorator {
        position: absolute;
        z-index: 1;
        opacity: 0.6;
        color: var(--primary-color);
        font-size: 24px;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
        100% { transform: translateY(0px); }
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .app-container {
            padding: 10px;
        }

        .card-body {
            padding: 20px;
        }
    }


    </style>
</head> 
<body>
    <div id="app">
        <div class="app-container">
            <!-- 装饰元素 -->
            <div class="decorator" style="top: 10%; left: 5%;">♥</div>
            <div class="decorator" style="top: 30%; right: 8%; animation-delay: 1s;">♥</div>
            <div class="decorator" style="bottom: 15%; left: 10%; animation-delay: 2s;">♥</div>

            <!-- 主卡片 -->
            <div class="main-card">
                <div class="card-header">
                    魔道后台 (◕‿◕✿)
                </div>
                <div class="card-body">
                    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
                        <el-form-item label="授权密码" prop="pwd">
                            <el-input
                                v-model="form.pwd"
                                placeholder="输入授权密码"
                                type="password"
                                show-password
                                maxlength="5000">
                            </el-input>
                        </el-form-item>

                        <el-form-item label="选择服务器" prop="qu">
                            <el-select v-model="form.qu" placeholder="请选择服务器" style="width: 100%">
                                <el-option
                                    v-for="server in servers"
                                    :key="server.value"
                                    :label="server.label"
                                    :value="server.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="用户账号" prop="uid">
                            <el-input
                                v-model="form.uid"
                                placeholder="请输入账号"
                                maxlength="5000">
                            </el-input>
                        </el-form-item>

                        <el-form-item label="选择物品" prop="mailid">
                            <el-select
                                v-model="form.mailid"
                                placeholder="请选择物品"
                                filterable
                                style="width: 100%">
                                <el-option
                                    v-for="item in items"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                    :title="item.label + '-' + item.desc">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="物品数量" prop="mailnum">
                            <el-input-number
                                v-model="form.mailnum"
                                :min="1"
                                :max="999999999999999"
                                placeholder="数量"
                                style="width: 100%">
                            </el-input-number>
                        </el-form-item>

                        <el-form-item>
                            <el-button
                                type="primary"
                                @click="sendMail"
                                :loading="loading"
                                style="width: 100%; height: 50px; font-size: 16px;">
                                {{ loading ? '发送中...' : '发送邮件' }}
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    loading: false,
                    form: {
                        pwd: '',
                        qu: '',
                        uid: '',
                        mailid: '',
                        mailnum: 1
                    },
                    rules: {
                        pwd: [
                            { required: true, message: '请输入授权密码', trigger: 'blur' }
                        ],
                        qu: [
                            { required: true, message: '请选择服务器', trigger: 'change' }
                        ],
                        uid: [
                            { required: true, message: '请输入用户账号', trigger: 'blur' }
                        ],
                        mailid: [
                            { required: true, message: '请选择物品', trigger: 'change' }
                        ],
                        mailnum: [
                            { required: true, message: '请输入物品数量', trigger: 'blur' }
                        ]
                    },
                    servers: <?php echo json_encode($servers); ?>,
                    items: <?php echo json_encode($items); ?>
                }
            },
            methods: {
                async sendMail() {
                    try {
                        await this.$refs.formRef.validate();
                        this.loading = true;

                        const formData = new FormData();
                        formData.append('action', 'send_mail');
                        formData.append('pwd', this.form.pwd);
                        formData.append('qu', this.form.qu);
                        formData.append('uid', this.form.uid);
                        formData.append('mailid', this.form.mailid);
                        formData.append('mailnum', this.form.mailnum);

                        const response = await axios.post(window.location.href, formData);

                        if (response.data.success) {
                            ElMessage.success(response.data.message);
                            // 可以选择重置表单
                            // this.$refs.formRef.resetFields();
                        } else {
                            ElMessage.error(response.data.message || '发送失败');
                        }
                    } catch (error) {
                        if (error.response) {
                            ElMessage.error('服务器错误：' + error.response.status);
                        } else {
                            console.error('表单验证失败:', error);
                        }
                    } finally {
                        this.loading = false;
                    }
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>