<!DOCTYPE html>
<html>   
<?php
include "head.php";
include_once "./user/config.php";
$t = time();
?>    
<html lang="zh-cn">
<head> 
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"/>
    <title>魔道修仙-玩家后台</title> 
    <meta name="applicable-device" content="pc,mobile" /> 
    <link href="https://lib.baomitu.com/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="assets/font-awesome.min.css" media="all">
    <link href="./assets/layui/css/layui.css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://lib.baomitu.com/jquery/3.1.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://lib.baomitu.com/clipboard.js/1.7.1/clipboard.min.js"></script>
    <!--[if lt IE 9]>
        <script src="//cdn.bootcss.com/html5shiv/3.7.3/html5shiv.min.js"></script>
        <script src="//cdn.bootcss.com/respond.js/1.4.2/respond.min.js"></script>
    <![endif]--> 
    <style>
    :root {
        --primary-color: #ff7eb9;
        --secondary-color: #ff65a3;
        --accent-color: #7afcff;
        --text-color: #5a3d5c;
        --bg-color: #fff0f5;
        --card-bg: rgba(255, 255, 255, 0.9);
        --shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
    }

    body {
        font-family: 'Noto Sans SC', sans-serif;
        background: var(--bg-color) url('<?= $conf[
            "gro"
        ] ?>') no-repeat center center fixed;
        background-size: cover;
        color: var(--text-color);
        min-height: 100vh;
        margin: 0;
        padding: 20px 0;
        position: relative;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 240, 245, 0.7);
        z-index: -1;
    }

    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .nav {
        background: var(--card-bg);
        padding: 20px;
        margin-top: 20px;
        border-radius: 15px;
        box-shadow: var(--shadow);
        border: 1px solid rgba(255, 126, 185, 0.2);
        backdrop-filter: blur(5px);
        transition: all 0.3s ease;
    }

    .nav:hover {
        box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
    }

    .nav_header {
        font-size: 18px;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .nav_header i {
        margin-right: 10px;
        font-size: 22px;
    }

    .list-group-item {
        margin-top: 15px;
        padding: 12px 15px;
        border-left: 5px solid var(--primary-color);
        border-radius: 0 8px 8px 0;
        background-color: rgba(255, 126, 185, 0.1);
        color: var(--text-color);
    }

    .layui-form {
        margin-top: 20px;
    }

    .layui-input {
        border-radius: 8px;
        border: 1px solid #ffb6c1;
        padding: 12px 15px;
        transition: all 0.3s;
    }

    .layui-input:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    .submit {
        width: 100%;
        margin-top: 15px;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        border: none;
        border-radius: 8px;
        padding: 1px;
        color: white;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(255, 105, 180, 0.3);
    }

    .submit:hover {
        background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 105, 180, 0.4);
    }

    .submit:active {
        transform: translateY(0);
    }

    #jxmsg {
        margin-top: 20px;
    }

    button[disabled], html input[disabled] {
        cursor: not-allowed;
        background-color: #f5f5f5 !important;
        color: #ccc !important;
    }

    .layui-btn {
        border-radius: 8px;
        transition: all 0.3s;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .input-group {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .input-group input {
        flex: 1;
        border-radius: 8px 0 0 8px !important;
    }

    .input-group button {
        margin-left: 0;
        border-radius: 0 8px 8px 0 !important;
        background: linear-gradient(45deg, #7afcff, #7ac8ff);
        border: none;
        color: #333;
    }

    .input-group button:hover {
        background: linear-gradient(45deg, #7ac8ff, #7afcff);
    }

    #videoPopup {
        transition: all 0.3s ease;
        background: rgba(0, 0, 0, 0.9);
    }

    #popupVideo {
        border-radius: 10px;
        box-shadow: 0 0 30px rgba(255, 126, 185, 0.7);
    }

    .footer {
        margin-top: 30px;
        text-align: center;
        padding: 20px;
        color: var(--text-color);
        font-size: 14px;
    }

    .footer a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .footer a:hover {
        text-decoration: underline;
    }

    /* 音乐播放器样式 */
    .music-player {
        background: var(--card-bg);
        padding: 15px;
        border-radius: 15px;
        box-shadow: var(--shadow);
        margin-top: 20px;
        border: 1px solid rgba(255, 126, 185, 0.2);
        backdrop-filter: blur(5px);
    }

    .music-player-header {
        font-size: 16px;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .music-player-header i {
        margin-right: 10px;
    }

    .music-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15px;
    }

    .music-info {
        flex: 1;
        margin-left: 15px;
        overflow: hidden;
    }

    .music-title {
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .music-artist {
        font-size: 12px;
        color: #888;
    }

    .music-progress {
        width: 100%;
        height: 4px;
        background: rgba(255, 126, 185, 0.2);
        border-radius: 2px;
        margin-top: 10px;
        cursor: pointer;
    }

    .music-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
        width: 0%;
        transition: width 0.1s;
    }

    .music-time {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #888;
        margin-top: 5px;
    }

    .music-buttons {
        display: flex;
        align-items: center;
    }

    .music-btn {
        background: none;
        border: none;
        color: var(--primary-color);
        font-size: 18px;
        cursor: pointer;
        margin: 0 5px;
        transition: all 0.2s;
    }

    .music-btn:hover {
        color: var(--secondary-color);
        transform: scale(1.1);
    }

    .music-btn-play {
        font-size: 24px;
    }

    .music-list {
        margin-top: 15px;
        max-height: 200px;
        overflow-y: auto;
    }

    .music-item {
        padding: 8px 10px;
        border-radius: 5px;
        margin-bottom: 5px;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
    }

    .music-item:hover {
        background: rgba(255, 126, 185, 0.1);
    }

    .music-item.active {
        background: rgba(255, 126, 185, 0.2);
        color: var(--primary-color);
    }

    .music-item-cover {
        width: 40px;
        height: 40px;
        border-radius: 5px;
        margin-right: 10px;
        object-fit: cover;
    }

    .music-item-info {
        flex: 1;
        overflow: hidden;
    }

    .music-item-title {
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .music-item-artist {
        font-size: 12px;
        color: #888;
    }

    /* 动画效果 */
    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
        100% { transform: translateY(0px); }
    }

    .float {
        animation: float 3s ease-in-out infinite;
    }

    /* 加载动画 */
    .loading {
        display: flex;
        justify-content: center;
        padding: 20px;
    }

    .loading-dot {
        width: 12px;
        height: 12px;
        margin: 0 5px;
        background-color: var(--primary-color);
        border-radius: 50%;
        animation: bounce 1.4s infinite ease-in-out both;
    }

    .loading-dot:nth-child(1) { animation-delay: -0.32s; }
    .loading-dot:nth-child(2) { animation-delay: -0.16s; }
    .loading-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes bounce {
        0%, 80%, 100% { transform: scale(0); }
        40% { transform: scale(1); }
    }

    /* 可爱装饰元素 */
    .decorator {
        position: absolute;
        z-index: -1;
        opacity: 0.6;
    }

    .decorator.heart {
        color: #ff7eb9;
        font-size: 24px;
    }

    /* 验证码间距调整 */
    .captcha-container {
        margin-top: 25px;
    }

    /* 卡密状态显示 */
    .km-status {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: var(--card-bg);
        padding: 10px 15px;
        border-radius: 15px;
        box-shadow: var(--shadow);
        border: 1px solid rgba(255, 126, 185, 0.2);
        backdrop-filter: blur(5px);
        z-index: 1000;
        font-size: 14px;
    }

    .km-status .km-count {
        color: var(--primary-color);
        font-weight: bold;
    }

    .km-status .km-expire {
        color: #888;
        font-size: 12px;
    }

    /* 卡密验证弹窗样式 */
    .swal2-km-input {
        padding: 12px 15px;
        border: 1px solid #ffb6c1;
        border-radius: 8px;
        font-size: 16px;
        color: #5a3d5c;
        transition: all 0.3s;
        width: 100%;
        box-sizing: border-box;
    }

    .swal2-km-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
        outline: none;
    }

    .swal2-km-title {
        color: var(--primary-color);
        font-weight: bold;
        margin-bottom: 15px;
    }

    .swal2-km-footer {
        margin-top: 15px;
        font-size: 14px;
        color: #888;
    }

    .swal2-km-footer a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .swal2-km-footer a:hover {
        text-decoration: underline;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .container {
            padding: 10px;
        }
        
        .input-group {
            flex-direction: column;
        }
        
        .input-group input {
            border-radius: 8px !important;
            margin-bottom: 5px;
        }
        
        .input-group button {
            border-radius: 8px !important;
            width: 100%;
            margin-left: 0;
        }
        
        .music-controls {
            flex-direction: column;
        }
        
        .music-info {
            margin-left: 0;
            margin-top: 10px;
            width: 100%;
        }
        
        .km-status {
            bottom: 10px;
            right: 10px;
            left: 10px;
            text-align: center;
        }
    }
    </style>
</head> 
<body>
<div class="container">
    <div class="decorator heart" style="top: 10%; left: 5%;">♥</div>
    <div class="decorator heart float" style="top: 30%; right: 8%;">♥</div>
    <div class="decorator heart" style="bottom: 15%; left: 10%;">♥</div>
    <!--解析-->
    <div class="nav">
        
        <div class="list-group-item">
            大千后台 (◕‿◕✿)
        </div>
                            <div class="list-group-item">
             自助授权
        </div>
        <div class="layui-form">
               <div class="layui-form">
                   <div class="layui-form">
                        <select id="dq" name="dq" class="selectpicker" required>
						<?php foreach ($quarr as $key => $value) {
          if ($value["hidde"] != true) {
              echo '<option value="' .
                  $key .
                  '">' .
                  $value["name"] .
                  "</option>";
          }
      } ?>
                        </select>
                </div>
                    <input type="text" id="cdk" name="cdk" class="layui-input" value="" placeholder="CDK" required>
                    <input type="text" id="username" name="username" class="layui-input" value="" placeholder="输入游戏账号" required>
                    <input type="text" id="passwd" name="passwd" class="layui-input" value="" placeholder="输入游戏密码" required>
                    </div>
                 <div class="layui-form">
                     <button type="submit" class="btn btn-info btn-sm btn-block" onclick="sendcdksq()">提交授权</button> 
                </div> 
                                            <div class="list-group-item">
                                            
             功能区
        </div>
        <div class="layui-form">
            <input type="text" id="pwd" name="pwd" placeholder="输入账号密码" class="layui-input" maxlength="5000">
        </div>
				<div class="layui-form">
                        <select id="qu" name="qu" class="selectpicker" required>
						<?php foreach ($quarr as $key => $value) {
          if ($value["hidde"] != true) {
              echo '<option value="' .
                  $key .
                  '">' .
                  $value["name"] .
                  "</option>";
          }
      } ?>
                        </select>
                </div>
		<div class="layui-form">
         <input type="text" id="uid" name="uid" class="layui-input" maxlength="5000" value="" placeholder="请输入账号" required>
		 </div>
		<div class="layui-form">
                        <select id="mailid" name="mailid" class="selectpicker layui-input form-control" data-live-search="true" title="请选择物品">
                         <?php
                         $file = fopen("user/players.txt", "r");
                         while (!feof($file)) {
                             $line = fgets($file);
                             $txts = explode(";", $line);
                             echo '<option value="' .
                                 $txts[0] .
                                 '" title="' .
                                 $txts[1] .
                                 "-" .
                                 $txts[2] .
                                 '">' .
                                 $txts[1] .
                                 "</option>";
                         }
                         fclose($file);
                         ?>							
                        </select>
                </div>
		 
		    <div class="layui-form">					    
                        <input type="text" id="mailnum" name="mailnum" class="layui-input" min="0" max="99999999" value="" placeholder="数量" required>
                    </div>
        <button type="submit" class="layui-btn submit" onclick="send_mail()">发送邮件</button> 
    </div> 

     <script src="js/index.js?v=<?php echo $t; ?>"></script>
</div>
</html>